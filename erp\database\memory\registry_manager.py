"""
Manager for database memory registries
"""

import asyncio
import time
from typing import Any, Dict, Optional

from ...logging import get_logger
from .app_registry import AppRegistry


class MemoryRegistryManager:
    """
    Manager for database memory registries
    Ensures only one registry per database and handles registry lifecycle
    """

    _registries: Dict[str, AppRegistry] = {}
    _lock = asyncio.Lock()
    _logger = get_logger(__name__)
    _app = None  # FastAPI app instance
    _base_module_cache: Dict[str, bool] = (
        {}
    )  # Cache for base module installation status
    _cache_lock = asyncio.Lock()  # Separate lock for cache operations

    @classmethod
    def set_app(cls, app):
        """Set the FastAPI app instance for the manager."""
        cls._app = app
        cls._logger.debug("FastAPI app instance set in MemoryRegistryManager.")

    @classmethod
    async def get_registry(
        cls, db_name: str, filter_result: Optional[Dict[str, Any]] = None
    ) -> AppRegistry:
        """
        Get or create registry for a database
        Ensures only one registry per database
        Only creates registry if Base module is installed successfully

        Args:
            db_name: Database name to get/create registry for
            filter_result: Optional pre-computed filter result to avoid duplicate processing
        """
        if not db_name or db_name.strip() == "":
            raise ValueError("Database name cannot be empty or None")

        # Check if Base module is installed before creating registry
        if not await cls._is_base_module_installed(db_name):
            raise RuntimeError(
                f"Base module is not installed in database {db_name}. Cannot create memory registry."
            )

        async with cls._lock:
            if db_name not in cls._registries:
                cls._logger.info(
                    f"Creating new memory registry for database: {db_name}"
                )
                registry = AppRegistry(db_name)
                cls._registries[db_name] = registry
                cls._logger.info(
                    f"Registry created for {db_name} with lazy route registration"
                )
            else:
                cls._logger.debug(
                    f"Returning existing memory registry for database: {db_name}"
                )

            registry = cls._registries[db_name]

            # Skip early route registration - routes will be registered after addon loading
            # This prevents discovering 0 routes before addons are imported
            cls._logger.debug(
                f"Registry accessed for {db_name}, routes will be registered after addon loading"
            )

            return registry

    @classmethod
    async def initialize_registry_with_delay(
        cls, db_name: str, delay_seconds: float = 0
    ) -> AppRegistry:
        """
        Initialize AppRegistry with realistic delay and full setup
        This method is used for one-time initialization from middleware, jobs, cron jobs, etc.

        Args:
            db_name: Database name to initialize registry for
            delay_seconds: Initialization delay in seconds (default: 0.0)

        Returns:
            AppRegistry: The initialized registry

        Raises:
            ValueError: If database name is invalid
            RuntimeError: If base module is not installed or initialization fails
        """
        if not db_name or db_name.strip() == "":
            raise ValueError("Database name cannot be empty or None")

        # Check if registry already exists
        if await cls.has_registry(db_name):
            cls._logger.debug(f"Using existing AppRegistry for database: {db_name}")
            return await cls.get_registry(db_name)

        # Only initialize if base module is installed
        if not await cls._is_base_module_installed(db_name):
            raise RuntimeError(
                f"Base module is not installed in database {db_name}. Cannot initialize memory registry."
            )

        from ...logging.coordination import operation_context

        operation_id = f"registry_init_{db_name}"
        with operation_context(operation_id, database=db_name) as should_execute:
            if not should_execute:
                cls._logger.debug(
                    f"Skipping duplicate registry initialization for {db_name}"
                )
                return cls._registries.get(db_name)

            try:
                cls._logger.info(f"Initializing AppRegistry for database: {db_name}")

                # Add realistic initialization delay
                import asyncio

                await asyncio.sleep(delay_seconds)

                # Create and initialize the registry
                registry = await cls.get_registry(db_name)

                # Load installed modules into registry with enhanced functionality
                await registry.refresh_from_database()

                addon_load_order = await registry.get_addon_load_order()
                cls._logger.info(
                    f"AppRegistry successfully initialized for database: {db_name} with {len(addon_load_order)} addons"
                )

                return registry

            except Exception as e:
                cls._logger.error(
                    f"Failed to initialize AppRegistry for database '{db_name}': {e}"
                )
                raise RuntimeError(
                    f"Registry initialization failed for database '{db_name}': {str(e)}"
                ) from e

    @classmethod
    async def _is_base_module_installed(cls, db_name: str) -> bool:
        """
        Check if Base module is installed in the database (with caching)
        """
        # Check cache first
        async with cls._cache_lock:
            if db_name in cls._base_module_cache:
                cls._logger.debug(
                    f"Using cached base module status for {db_name}: {cls._base_module_cache[db_name]}"
                )
                return cls._base_module_cache[db_name]

        try:
            from ..registry.database_registry import DatabaseRegistry

            db_manager = await DatabaseRegistry.get_database(db_name)

            # Check if ir_module_module table exists and base module is installed
            check_query = """
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables
                    WHERE table_name = 'ir_module_module'
                ) as table_exists
            """
            result = await db_manager.fetchrow(check_query)
            print(result)

            if not result or not result["table_exists"]:
                cls._logger.warning(
                    f"ir_module_module table does not exist in database {db_name}"
                )
                # Cache negative result
                async with cls._cache_lock:
                    cls._base_module_cache[db_name] = False
                return False

            # Check if base module is installed
            module_query = """
                SELECT state FROM ir_module_module
                WHERE name = 'base' AND state = 'installed'
            """
            module_result = await db_manager.fetchrow(module_query)

            is_installed = bool(module_result)

            # Cache the result
            async with cls._cache_lock:
                cls._base_module_cache[db_name] = is_installed

            if is_installed:
                cls._logger.info(f"Base module is installed in database {db_name}")
            else:
                cls._logger.warning(
                    f"Base module is not installed in database {db_name}"
                )

            return is_installed

        except Exception as e:
            cls._logger.error(
                f"Error checking Base module installation in {db_name}: {e}"
            )
            # Cache negative result on error
            async with cls._cache_lock:
                cls._base_module_cache[db_name] = False
            return False

    @classmethod
    async def has_registry(cls, db_name: str) -> bool:
        """Check if registry exists for database"""
        async with cls._lock:
            return db_name in cls._registries

    @classmethod
    async def is_registry_fully_initialized(cls, db_name: str) -> bool:
        """Check if registry exists and is fully initialized with routes registered"""
        async with cls._lock:
            if db_name not in cls._registries:
                return False

            registry = cls._registries[db_name]
            # Check if routes are registered and addons are loaded
            return (
                registry.route_manager._routes_registered
                and len(registry.installed_modules) > 0
            )

    @classmethod
    async def clear_base_module_cache(cls, db_name: str = None) -> None:
        """Clear base module installation cache for specific database or all databases"""
        async with cls._cache_lock:
            if db_name:
                cls._base_module_cache.pop(db_name, None)
                cls._logger.debug(f"Cleared base module cache for database: {db_name}")
            else:
                cls._base_module_cache.clear()
                cls._logger.debug("Cleared all base module cache entries")

    @classmethod
    async def remove_registry(cls, db_name: str) -> bool:
        """Remove registry for database"""
        async with cls._lock:
            if db_name in cls._registries:
                registry = cls._registries[db_name]
                cls._logger.info(f"Removing memory registry for database: {db_name}")
                del cls._registries[db_name]
                return True
            return False

    @classmethod
    async def get_all_registries(cls) -> Dict[str, AppRegistry]:
        """Get all active registries"""
        async with cls._lock:
            return cls._registries.copy()

    @classmethod
    async def get_registry_stats(cls) -> Dict[str, Any]:
        """Get statistics for all registries"""
        async with cls._lock:
            stats = {"total_registries": len(cls._registries), "registries": {}}

            for db_name, registry in cls._registries.items():
                stats["registries"][db_name] = registry.get_stats()

            return stats

    @classmethod
    async def cleanup_inactive_registries(cls, max_idle_time: float = 3600) -> int:
        """
        Clean up registries that have no active environments and are idle
        Returns number of registries cleaned up
        """
        async with cls._lock:
            current_time = time.time()
            to_remove = []

            for db_name, registry in cls._registries.items():
                # Check if registry has no active environments and is idle
                if (
                    registry.get_active_environments_count() == 0
                    and current_time - registry.created_at > max_idle_time
                ):
                    to_remove.append(db_name)

            for db_name in to_remove:
                cls._logger.info(
                    f"Cleaning up inactive registry for database: {db_name}"
                )
                del cls._registries[db_name]

            return len(to_remove)

    @classmethod
    async def refresh_registry(cls, db_name: str) -> None:
        """
        Refresh a specific registry from its database

        Args:
            db_name: Database name to refresh

        Raises:
            Exception if refresh fails
        """
        registry = await cls.get_registry(db_name)
        await registry.refresh_from_database()
        cls._logger.debug("Registry refreshed for database: %s", db_name)

    @classmethod
    async def update_registry_after_module_action(
        cls, db_name: str, module_name: str, action: str
    ) -> None:
        """
        Update a specific registry after a module action

        Args:
            db_name: Database name
            module_name: Module name
            action: Action performed (install/uninstall/upgrade)

        Raises:
            Exception if update fails
        """
        # Clear cache to ensure fresh data is loaded
        async with cls._cache_lock:
            if db_name in cls._base_module_cache:
                del cls._base_module_cache[db_name]
                cls._logger.debug("Cleared base module cache for %s", db_name)

        registry = await cls.get_registry(db_name)
        await registry.update_registry_after_module_action(module_name, action)
        cls._logger.debug(
            "Registry updated for %s after %s of module %s",
            db_name,
            action,
            module_name,
        )
