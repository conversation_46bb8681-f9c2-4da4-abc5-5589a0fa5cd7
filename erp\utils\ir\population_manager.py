"""
IR Population Manager
Main manager for IR model and fields population during addon lifecycle
"""

from datetime import datetime
from typing import Any, Dict, List

from ...logging import get_logger
from .metadata import IRMetadataOperations

logger = get_logger(__name__)


class IRPopulationManager:
    """
    Core manager for IR model and fields population during addon lifecycle.

    This class ensures that IR metadata population:
    1. Only happens after successful schema synchronization
    2. Is part of the core addon install/upgrade process
    3. Handles all registered models, not just base models
    4. Provides proper error handling and rollback support
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.metadata_ops = IRMetadataOperations()

    async def populate_ir_metadata(
        self,
        db_manager,
        addon_name: str = None,
        schema_sync_results: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Populate IR model and fields metadata after successful schema synchronization.

        This is the main entry point for IR population during addon install/upgrade.

        Args:
            db_manager: Database manager instance
            addon_name: Name of the addon being installed/upgraded (None for all models)
            schema_sync_results: Results from schema synchronization to verify success

        Returns:
            Dictionary containing population results and statistics
        """
        self.logger.info(
            f"Starting IR metadata population for addon: {addon_name or 'all'}"
        )

        # Verify schema sync was successful before proceeding
        if schema_sync_results and not self._verify_schema_sync_success(
            schema_sync_results
        ):
            return {
                "status": "error",
                "message": "Schema synchronization failed - IR population aborted",
                "schema_errors": schema_sync_results.get("errors", []),
            }

        try:
            # Get models to populate (returns dict of model_name -> model_class)
            models_to_populate = await self._get_models_for_population(addon_name)

            if not models_to_populate:
                self.logger.warning(
                    f"No models found for IR population (addon: {addon_name})"
                )
                return {
                    "status": "success",
                    "message": "No models to populate",
                    "models_processed": 0,
                    "fields_processed": 0,
                }

            # Populate IR metadata for each model
            results = await self._populate_models_metadata(
                db_manager, models_to_populate
            )

            self.logger.info(
                f"IR metadata population completed: {results['models_processed']} models, "
                f"{results['fields_processed']} fields"
            )

            return results

        except Exception as e:
            error_msg = f"Failed to populate IR metadata: {e}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg, "exception": str(e)}

    def _verify_schema_sync_success(self, schema_sync_results: Dict[str, Any]) -> bool:
        """
        Verify that schema synchronization completed successfully.

        Args:
            schema_sync_results: Results from schema sync operation

        Returns:
            True if schema sync was successful, False otherwise
        """
        if not schema_sync_results:
            return True  # No results means we proceed (backward compatibility)

        status = schema_sync_results.get("status")
        errors = schema_sync_results.get("errors", [])

        if status == "error":
            self.logger.error(f"Schema sync failed with status: {status}")
            return False

        if errors:
            self.logger.error(f"Schema sync had errors: {errors}")
            return False

        self.logger.debug("Schema synchronization verified as successful")
        return True

    async def _get_models_for_population(self, addon_name: str = None) -> Dict[str, type]:
        """
        Get dictionary of models that need IR metadata population.

        Enhanced to handle all registered models comprehensively, with proper
        filtering by addon when possible.

        Args:
            addon_name: Specific addon name, or None for all models

        Returns:
            Dictionary mapping model names to model classes
        """
        try:
            from ...models.lifecycle_manager import create_addon_model_registry

            # Create a temporary registry for the addon
            model_registry = create_addon_model_registry(addon_name or "base")
            all_models = model_registry.all()

            if addon_name is None:
                # Return all registered models
                self.logger.info(f"Found {len(all_models)} models for IR population")
                return all_models

            # Try to filter models by addon
            filtered_model_names = self._filter_models_by_addon(all_models, addon_name)

            if not filtered_model_names:
                # If no addon-specific filtering possible, return all models
                # This ensures IR population works even without addon-model mapping
                self.logger.warning(
                    f"Could not filter models for addon '{addon_name}', using all models"
                )
                filtered_models = all_models
            else:
                # Create filtered dictionary
                filtered_models = {name: all_models[name] for name in filtered_model_names if name in all_models}

            self.logger.info(
                f"Found {len(filtered_models)} models for addon '{addon_name}' IR population"
            )
            return filtered_models

        except Exception as e:
            self.logger.error(f"Failed to get models for population: {e}")
            return {}

    def _filter_models_by_addon(self, all_models: Dict, addon_name: str) -> List[str]:
        """
        Filter models by addon name based on model module information.

        Args:
            all_models: Dictionary of all registered models
            addon_name: Name of the addon to filter by

        Returns:
            List of model names belonging to the specified addon
        """
        try:
            filtered_models = []

            for model_name, model_class in all_models.items():
                try:
                    # Try to determine addon from model class module
                    if hasattr(model_class, "__module__"):
                        module_parts = model_class.__module__.split(".")

                        # Check if module follows pattern: erp.addons.{addon_name}.models.*
                        # or addons.{addon_name}.models.*
                        if len(module_parts) >= 3:
                            if (
                                module_parts[0] == "erp"
                                and module_parts[1] == "addons"
                                and len(module_parts) >= 4
                                and module_parts[2] == addon_name
                            ):
                                filtered_models.append(model_name)
                            elif (
                                module_parts[0] == "addons"
                                and len(module_parts) >= 3
                                and module_parts[1] == addon_name
                            ):
                                filtered_models.append(model_name)

                    # Also check for base models when addon is 'base'
                    if addon_name == "base" and model_name.startswith("ir."):
                        if model_name not in filtered_models:
                            filtered_models.append(model_name)

                except Exception as e:
                    self.logger.debug(
                        f"Could not determine addon for model {model_name}: {e}"
                    )
                    continue

            return filtered_models

        except Exception as e:
            self.logger.error(f"Error filtering models by addon '{addon_name}': {e}")
            return []

    async def _populate_models_metadata(
        self, db_manager, models: Dict[str, type]
    ) -> Dict[str, Any]:
        """
        Populate IR metadata for a dictionary of models.

        Args:
            db_manager: Database manager instance
            models: Dictionary mapping model names to model classes

        Returns:
            Dictionary with population results
        """
        results = {
            "status": "success",
            "models_processed": 0,
            "fields_processed": 0,
            "errors": [],
            "details": {},
        }

        for model_name, model_class in models.items():
            try:
                model_result = await self._populate_single_model_metadata(
                    db_manager, model_name, model_class
                )

                if model_result["success"]:
                    results["models_processed"] += 1
                    results["fields_processed"] += model_result["fields_count"]
                    results["details"][model_name] = "success"
                    self.logger.debug(
                        f"✓ Populated IR metadata for {model_name} "
                        f"({model_result['fields_count']} fields)"
                    )
                else:
                    results["errors"].append(
                        f"Failed to populate {model_name}: {model_result['error']}"
                    )
                    results["details"][model_name] = "error"

            except Exception as e:
                error_msg = f"Error processing model {model_name}: {e}"
                results["errors"].append(error_msg)
                results["details"][model_name] = "error"
                self.logger.error(error_msg)

        if results["errors"]:
            results["status"] = (
                "partial" if results["models_processed"] > 0 else "error"
            )

        return results

    async def _populate_single_model_metadata(
        self, db_manager, model_name: str, model_class: type
    ) -> Dict[str, Any]:
        """
        Populate IR metadata for a single model.

        Args:
            db_manager: Database manager instance
            model_name: Name of the model to populate
            model_class: The model class

        Returns:
            Dictionary with single model population results
        """
        try:
            # Get model schema from the model class directly
            from ..schema.generator import SchemaGenerator

            model_schema = SchemaGenerator._extract_schema_from_model_class(model_class)

            if not model_schema:
                return {
                    "success": False,
                    "error": f"Could not get schema for model: {model_name}",
                    "fields_count": 0,
                }

            # Register model in ir.model table
            model_registered = await self.metadata_ops.register_model_metadata(
                db_manager, model_name, model_schema
            )

            if not model_registered:
                return {
                    "success": False,
                    "error": f"Failed to register model metadata for: {model_name}",
                    "fields_count": 0,
                }

            # Register model fields in ir.model.fields table
            fields_count = await self.metadata_ops.register_model_fields_metadata(
                db_manager, model_name, model_schema
            )

            return {"success": True, "fields_count": fields_count, "error": None}

        except Exception as e:
            return {"success": False, "error": str(e), "fields_count": 0}

    async def populate_ir_metadata_with_rollback(
        self,
        db_manager,
        addon_name: str = None,
        schema_sync_results: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Populate IR metadata with rollback support for addon installation failures.

        This method creates a savepoint before IR population and can rollback
        if the operation fails, ensuring database consistency.

        Args:
            db_manager: Database manager instance
            addon_name: Name of the addon being installed/upgraded
            schema_sync_results: Results from schema synchronization

        Returns:
            Dictionary containing population results with rollback information
        """
        savepoint_name = f"ir_population_{addon_name or 'all'}_{int(datetime.now().timestamp() * 1000)}"

        try:
            self.logger.info(
                f"Starting IR population with rollback support for addon: {addon_name or 'all'}"
            )

            # Create savepoint before IR population
            self.logger.debug(f"Creating savepoint: {savepoint_name}")
            await db_manager.savepoint(savepoint_name)

            # Perform IR population
            results = await self.populate_ir_metadata(
                db_manager, addon_name, schema_sync_results
            )

            if results.get("status") == "error":
                # Rollback on error
                self.logger.error(
                    "IR population failed, rolling back to savepoint: %s",
                    savepoint_name,
                )
                await db_manager.rollback_to_savepoint(savepoint_name)
                await db_manager.release_savepoint(savepoint_name)

                error_msg = results.get("message", "IR population failed")
                raise RuntimeError(f"IR population failed: {error_msg}")

            # Release savepoint on success
            self.logger.debug(
                f"IR population successful, releasing savepoint: {savepoint_name}"
            )
            await db_manager.release_savepoint(savepoint_name)

            results["rollback_performed"] = False
            results["savepoint_name"] = savepoint_name
            return results

        except Exception as e:
            error_msg = f"Critical error during IR population with rollback: {e}"
            self.logger.error(error_msg)

            try:
                # Attempt rollback on critical error
                await db_manager.rollback_to_savepoint(savepoint_name)
                await db_manager.release_savepoint(savepoint_name)
                rollback_successful = True
            except Exception as rollback_error:
                self.logger.error(
                    f"Failed to rollback savepoint {savepoint_name}: {rollback_error}"
                )
                rollback_successful = False

            return {
                "status": "error",
                "message": error_msg,
                "exception": str(e),
                "rollback_performed": rollback_successful,
                "savepoint_name": savepoint_name,
                "addon_name": addon_name,
            }

    async def discover_all_addon_models(self) -> Dict[str, List[str]]:
        """
        Discover all models organized by addon.

        Returns:
            Dictionary mapping addon names to lists of their model names
        """
        try:
            from ...models.lifecycle_manager import create_addon_model_registry

            # Create a temporary registry to get all models
            model_registry = create_addon_model_registry(
                "base"
            )  # Use base to get all models
            all_models = model_registry.all()
            addon_models = {}

            for model_name, model_class in all_models.items():
                addon_name = self._determine_model_addon(model_class)

                if addon_name not in addon_models:
                    addon_models[addon_name] = []

                addon_models[addon_name].append(model_name)

            self.logger.info(f"Discovered models for {len(addon_models)} addons")
            return addon_models

        except Exception as e:
            self.logger.error(f"Failed to discover addon models: {e}")
            return {}

    def _determine_model_addon(self, model_class) -> str:
        """
        Determine which addon a model belongs to.

        Args:
            model_class: The model class

        Returns:
            Addon name or 'unknown' if cannot be determined
        """
        try:
            if hasattr(model_class, "__module__"):
                module_parts = model_class.__module__.split(".")

                # Pattern: erp.addons.{addon_name}.models.*
                if (
                    len(module_parts) >= 4
                    and module_parts[0] == "erp"
                    and module_parts[1] == "addons"
                ):
                    return module_parts[2]

                # Pattern: addons.{addon_name}.models.*
                if len(module_parts) >= 3 and module_parts[0] == "addons":
                    return module_parts[1]

                # Special case for base models
                if hasattr(model_class, "_name") and model_class._name.startswith(
                    "ir."
                ):
                    return "base"

            return "unknown"

        except Exception:
            return "unknown"

    async def populate_all_models_ir_metadata(self, db_manager) -> Dict[str, Any]:
        """
        Populate IR metadata for all registered models across all addons.

        This is a comprehensive method that ensures all models in the system
        have their IR metadata properly populated.

        Args:
            db_manager: Database manager instance

        Returns:
            Dictionary containing comprehensive population results
        """
        self.logger.info("Starting comprehensive IR metadata population for all models")

        try:
            # Discover all models by addon
            addon_models = await self.discover_all_addon_models()

            results = {
                "status": "success",
                "total_addons": len(addon_models),
                "addons_processed": 0,
                "addons_failed": 0,
                "total_models": 0,
                "models_processed": 0,
                "fields_processed": 0,
                "errors": [],
                "addon_details": {},
            }

            # Process each addon's models
            for addon_name, model_names in addon_models.items():
                try:
                    self.logger.info(
                        f"Processing {len(model_names)} models for addon: {addon_name}"
                    )

                    addon_result = await self._populate_models_metadata(
                        db_manager, model_names
                    )
                    results["addon_details"][addon_name] = addon_result
                    results["total_models"] += len(model_names)

                    if addon_result.get("status") == "error":
                        results["addons_failed"] += 1
                        results["errors"].extend(addon_result.get("errors", []))
                    else:
                        results["addons_processed"] += 1
                        results["models_processed"] += addon_result.get(
                            "models_processed", 0
                        )
                        results["fields_processed"] += addon_result.get(
                            "fields_processed", 0
                        )

                except Exception as e:
                    error_msg = f"Failed to process addon '{addon_name}': {e}"
                    results["errors"].append(error_msg)
                    results["addons_failed"] += 1
                    self.logger.error(error_msg)

            # Determine overall status
            if results["addons_failed"] > 0:
                if results["addons_processed"] > 0:
                    results["status"] = "partial"
                else:
                    results["status"] = "error"

            self.logger.info(
                f"Comprehensive IR population completed: "
                f"{results['addons_processed']}/{results['total_addons']} addons, "
                f"{results['models_processed']} models, "
                f"{results['fields_processed']} fields"
            )

            return results

        except Exception as e:
            error_msg = f"Failed comprehensive IR metadata population: {e}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg,
                "total_addons": 0,
                "addons_processed": 0,
                "addons_failed": 0,
                "total_models": 0,
                "models_processed": 0,
                "fields_processed": 0,
                "errors": [error_msg],
                "addon_details": {},
            }


# Global instance for easy access
ir_population_manager = IRPopulationManager()
