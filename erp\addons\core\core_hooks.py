"""
Core addon installation hooks that provide IR population as a standard feature.

This module provides core hooks that are automatically executed during addon
installation and upgrade to ensure IR model and fields population happens
consistently across all addons.
"""

from typing import Any, Dict

from ...logging import get_logger
from ...utils.schema import Schema<PERSON>omparator
from ..hooks import Hook<PERSON>ontext, HookType, post_install_hook

logger = get_logger(__name__)


@post_install_hook(
    addon_name=None, priority=100
)  # Low priority to run after addon-specific hooks
async def core_ir_population_hook(context: HookContext) -> bool:
    """
    Core post-install hook that ensures IR model and fields population
    for any addon installation or upgrade.

    This hook runs after all addon-specific hooks and ensures that:
    1. Database schema is synchronized
    2. IR metadata is populated only after successful schema sync
    3. All models from the installed addon are properly registered

    Args:
        context: Hook context containing addon name, environment, etc.

    Returns:
        True if IR population successful, False otherwise
    """
    if not context.env:
        logger.error("No environment available for core IR population")
        return False

    addon_name = context.addon_name
    logger.info(f"CORE IR POPULATION: Starting for addon '{addon_name}'")

    try:
        db_manager = context.env.cr

        # Perform schema sync and IR population with rollback support
        logger.info(f"Performing schema sync and IR population for addon: {addon_name}")

        # First perform schema sync
        schema_results = await SchemaComparator.sync_model_tables(addon_name)

        if not schema_results.get("sync_successful", False):
            logger.error(
                f"Schema sync failed for addon '{addon_name}': {schema_results.get('message')}"
            )
            return False

        # Then perform IR population with rollback support
        from ...utils.ir import ir_population_manager

        ir_results = await ir_population_manager.populate_ir_metadata_with_rollback(
            db_manager, addon_name, schema_results
        )

        if ir_results.get("status") == "error":
            logger.error(
                f"Core IR population failed for addon '{addon_name}': {ir_results.get('message')}"
            )
            if ir_results.get("rollback_performed"):
                logger.info(
                    f"IR population changes were rolled back for addon '{addon_name}'"
                )
            return False

        # Log summary
        schema_summary = schema_results.get("summary", schema_results)
        ir_summary = ir_results
        logger.info(
            f"✅ Core IR population completed for addon '{addon_name}': "
            f"{schema_summary.get('tables_created', 0)} tables created, "
            f"{ir_summary.get('models_processed', 0)} models populated, "
            f"{ir_summary.get('fields_processed', 0)} fields populated"
        )

        return True

    except Exception as e:
        logger.error(f"CORE IR POPULATION: Error for addon '{addon_name}': {e}")
        return False


class AddonIRManager:
    """
    Manager class for handling IR population during addon lifecycle.

    This class provides methods to ensure IR population is properly
    integrated into addon installation and upgrade processes.
    """

    def __init__(self):
        self.logger = get_logger(__name__)

    async def ensure_ir_population_for_addon(
        self, env, addon_name: str
    ) -> Dict[str, Any]:
        """
        Ensure IR population is completed for a specific addon.

        This method can be called manually to ensure an addon's IR metadata
        is properly populated, typically used during addon upgrades or
        when IR data needs to be refreshed.

        Args:
            env: Environment instance
            addon_name: Name of the addon

        Returns:
            Dictionary with operation results
        """
        context = HookContext(
            hook_type=HookType.POST_INSTALL, addon_name=addon_name, env=env, metadata={}
        )

        success = await core_ir_population_hook(context)

        return {
            "success": success,
            "addon_name": addon_name,
            "operation": "ir_population",
        }

    async def validate_ir_population_for_addon(
        self, env, addon_name: str
    ) -> Dict[str, Any]:
        """
        Validate that IR population is complete for an addon.

        This method checks if all models and fields for an addon
        are properly registered in the IR tables.

        Args:
            env: Environment instance
            addon_name: Name of the addon

        Returns:
            Dictionary with validation results
        """
        try:
            # Get all models for the addon
            from ...utils.ir import ir_population_manager

            validation_results = (
                await ir_population_manager.validate_ir_metadata_for_addon(
                    env.cr, addon_name
                )
            )

            return {
                "success": True,
                "addon_name": addon_name,
                "validation_results": validation_results,
            }

        except Exception as e:
            self.logger.error(f"IR validation failed for addon '{addon_name}': {e}")
            return {"success": False, "addon_name": addon_name, "error": str(e)}


# Global instance for easy access
addon_ir_manager = AddonIRManager()
